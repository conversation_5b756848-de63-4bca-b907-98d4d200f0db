#!/usr/bin/env python3
"""
Functional test for assign-4.py
This script tests the main functionality with mock data
"""

import sys
import os
import datetime
from unittest.mock import Mock, patch, MagicMock

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the module
import importlib.util
spec = importlib.util.spec_from_file_location("assign4", "assign-4.py")
assign4 = importlib.util.module_from_spec(spec)

def create_mock_jira_issue(key, status, assignee, custom_field_value):
    """Create a mock JIRA issue for testing"""
    return {
        'key': key,
        'fields': {
            'customfield_111118': custom_field_value,
            'summary': f'Test Issue {key}',
            'status': {'name': status},
            'assignee': {'name': assignee} if assignee else None,
            'components': []
        }
    }

def test_main_workflow():
    """Test the main workflow with mock data"""
    print("Testing main workflow...")
    
    # Create mock issues
    mock_issues = [
        create_mock_jira_issue('AT-12345', 'Open', 'testuser1', 'https://example.com/page/test-page-1'),
        create_mock_jira_issue('AT-12346', 'Transcribed', 'testuser2', 'https://example.com/page/test-page-2'),
        create_mock_jira_issue('AT-12347', 'I Proofed', None, 'https://example.com/page/test-page-3'),
    ]
    
    # Mock all external dependencies
    with patch.object(assign4, 'get_jira_data', return_value=mock_issues) as mock_get_jira, \
         patch.object(assign4, 'get_page_id') as mock_get_page_id, \
         patch.object(assign4, 'get_current_conf_data') as mock_get_conf, \
         patch.object(assign4, 'change_or_no') as mock_change_check, \
         patch.object(assign4, 'set_conf_status') as mock_set_status, \
         patch.object(assign4, 'set_conf_assignee') as mock_set_assignee, \
         patch.object(assign4, 'do_remove_assignee_process', return_value='') as mock_remove, \
         patch('builtins.open', create=True) as mock_open, \
         patch.object(assign4, 'worksheet') as mock_worksheet:
        
        # Configure mocks
        mock_get_page_id.return_value = '12345'  # Valid page ID
        mock_get_conf.return_value = ['transcription', 'olduser']  # Current conf state
        mock_change_check.return_value = 1  # Change needed
        mock_worksheet.insert_row = Mock()
        
        try:
            # Reset global counters
            assign4.totalupdates = 0
            assign4.success = 1
            
            # Run the main function
            assign4.main()
            
            # Verify the results
            print(f"✓ Processed {len(mock_issues)} issues")
            print(f"✓ Total updates: {assign4.totalupdates}")
            print(f"✓ get_jira_data called: {mock_get_jira.called}")
            print(f"✓ get_page_id called {mock_get_page_id.call_count} times")
            print(f"✓ Confluence status updates called {mock_set_status.call_count} times")
            
            return True
            
        except Exception as e:
            print(f"✗ Main workflow test failed: {str(e)}")
            return False

def test_status_mapping():
    """Test different JIRA status to Confluence status mappings"""
    print("\nTesting status mappings...")
    
    status_tests = [
        ('Open', 1),
        ('Assigned TR', 1),
        ('TR in Progress', 1),
        ('Transcribed', 2),
        ('Assigned IP', 2),
        ('IP in Progress', 2),
        ('I Proofed', 3),
        ('Assigned IIP', 3),
        ('IIP in Progress', 3),
        ('II Proofed', 4),
        ('Assigned PR', 4),
        ('PR in Progress', 4),
        ('Proof-Read', 5),
        ('On Hold', 6),
    ]
    
    passed = 0
    total = len(status_tests)
    
    for jira_status, expected_conf_status in status_tests:
        mock_issue = create_mock_jira_issue('AT-TEST', jira_status, 'testuser', 'https://example.com/page/test')
        
        with patch.object(assign4, 'get_page_id', return_value='12345'), \
             patch.object(assign4, 'get_current_conf_data', return_value=['transcription', '']), \
             patch.object(assign4, 'change_or_no', return_value=1), \
             patch.object(assign4, 'set_conf_status') as mock_set_status, \
             patch.object(assign4, 'set_conf_assignee'), \
             patch('builtins.open', create=True), \
             patch.object(assign4, 'do_remove_assignee_process', return_value=''), \
             patch.object(assign4, 'worksheet') as mock_worksheet:
            
            mock_worksheet.insert_row = Mock()
            
            try:
                assign4.update_confluence(mock_issue)
                
                # Check if set_conf_status was called with the expected status
                if mock_set_status.called:
                    called_status = mock_set_status.call_args[0][0]
                    if called_status == expected_conf_status:
                        print(f"✓ {jira_status} -> Confluence status {expected_conf_status}")
                        passed += 1
                    else:
                        print(f"✗ {jira_status} -> Expected {expected_conf_status}, got {called_status}")
                else:
                    print(f"✗ {jira_status} -> set_conf_status not called")
                    
            except Exception as e:
                print(f"✗ {jira_status} -> Error: {str(e)}")
    
    print(f"\nStatus mapping tests: {passed}/{total} passed")
    return passed == total

def run_functional_tests():
    """Run all functional tests"""
    print("Running functional tests for assign-4.py")
    print("=" * 60)
    
    # Initialize the module
    try:
        spec.loader.exec_module(assign4)
        # Initialize global variables
        assign4.m = {}
        assign4.totalupdates = 0
        assign4.success = 1
        print("✓ Module loaded successfully")
    except Exception as e:
        print(f"✗ Failed to load module: {str(e)}")
        return False
    
    tests_passed = 0
    total_tests = 2
    
    # Test main workflow
    if test_main_workflow():
        tests_passed += 1
    
    # Test status mappings
    if test_status_mapping():
        tests_passed += 1
    
    print("\n" + "=" * 60)
    print(f"Functional Test Results: {tests_passed}/{total_tests} test suites passed")
    
    if tests_passed == total_tests:
        print("✓ All functional tests passed!")
        print("The assign-4.py script is working correctly and ready for use.")
        return True
    else:
        print("✗ Some functional tests failed.")
        return False

if __name__ == '__main__':
    success = run_functional_tests()
    sys.exit(0 if success else 1)

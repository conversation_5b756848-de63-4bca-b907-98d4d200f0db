Metadata-Version: 2.1
Name: cryptography
Version: 2.8
Summary: cryptography is a package which provides cryptographic recipes and primitives to Python developers.
Home-page: https://github.com/pyca/cryptography
Author: The cryptography developers
Author-email: <EMAIL>
License: BSD or Apache License, Version 2.0
Description: pyca/cryptography
        =================
        
        .. image:: https://img.shields.io/pypi/v/cryptography.svg
            :target: https://pypi.org/project/cryptography/
            :alt: Latest Version
        
        .. image:: https://readthedocs.org/projects/cryptography/badge/?version=latest
            :target: https://cryptography.io
            :alt: Latest Docs
        
        .. image:: https://travis-ci.org/pyca/cryptography.svg?branch=master
            :target: https://travis-ci.org/pyca/cryptography
        
        .. image:: https://dev.azure.com/pyca/cryptography/_apis/build/status/Azure%20CI?branchName=master
            :target: https://dev.azure.com/pyca/cryptography/_build/latest?definitionId=3&branchName=master
        
        .. image:: https://codecov.io/github/pyca/cryptography/coverage.svg?branch=master
            :target: https://codecov.io/github/pyca/cryptography?branch=master
        
        
        ``cryptography`` is a package which provides cryptographic recipes and
        primitives to Python developers.  Our goal is for it to be your "cryptographic
        standard library". It supports Python 2.7, Python 3.4+, and PyPy 5.4+.
        
        ``cryptography`` includes both high level recipes and low level interfaces to
        common cryptographic algorithms such as symmetric ciphers, message digests, and
        key derivation functions. For example, to encrypt something with
        ``cryptography``'s high level symmetric encryption recipe:
        
        .. code-block:: pycon
        
            >>> from cryptography.fernet import Fernet
            >>> # Put this somewhere safe!
            >>> key = Fernet.generate_key()
            >>> f = Fernet(key)
            >>> token = f.encrypt(b"A really secret message. Not for prying eyes.")
            >>> token
            '...'
            >>> f.decrypt(token)
            'A really secret message. Not for prying eyes.'
        
        You can find more information in the `documentation`_.
        
        You can install ``cryptography`` with:
        
        .. code-block:: console
        
            $ pip install cryptography
        
        For full details see `the installation documentation`_.
        
        Discussion
        ~~~~~~~~~~
        
        If you run into bugs, you can file them in our `issue tracker`_.
        
        We maintain a `cryptography-dev`_ mailing list for development discussion.
        
        You can also join ``#cryptography-dev`` on Freenode to ask questions or get
        involved.
        
        Security
        ~~~~~~~~
        
        Need to report a security issue? Please consult our `security reporting`_
        documentation.
        
        
        .. _`documentation`: https://cryptography.io/
        .. _`the installation documentation`: https://cryptography.io/en/latest/installation/
        .. _`issue tracker`: https://github.com/pyca/cryptography/issues
        .. _`cryptography-dev`: https://mail.python.org/mailman/listinfo/cryptography-dev
        .. _`security reporting`: https://cryptography.io/en/latest/security/
        
Platform: UNKNOWN
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Apache Software License
Classifier: License :: OSI Approved :: BSD License
Classifier: Natural Language :: English
Classifier: Operating System :: MacOS :: MacOS X
Classifier: Operating System :: POSIX
Classifier: Operating System :: POSIX :: BSD
Classifier: Operating System :: POSIX :: Linux
Classifier: Operating System :: Microsoft :: Windows
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 2
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.4
Classifier: Programming Language :: Python :: 3.5
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Topic :: Security :: Cryptography
Requires-Python: >=2.7,!=3.0.*,!=3.1.*,!=3.2.*,!=3.3.*
Description-Content-Type: text/x-rst
Provides-Extra: docs
Provides-Extra: docstest
Provides-Extra: idna
Provides-Extra: pep8test
Provides-Extra: test

import os, string, re
import requests
from requests.auth import HTTPBasicAuth
import json
import time
from datetime import datetime
# import diff_match_patch #as dmp_module
from bs4 import BeautifulSoup
import html.parser

if os.name == 'posix': from fast_diff_match_patch import diff

if os.name == 'posix':
	linuxDir='/home/<USER>/files/diff_mom/'
else:
	linuxDir=''
mapdict={}
nextEmpty=19968
mapdictrev={}
specialchar_range = range(19968,40959)
r = specialchar_range

def storeinmap(string):
	global nextEmpty
	if string in mapdictrev.keys():
		return mapdictrev[string]

	mapdict[chr(nextEmpty)] = string
	mapdictrev[string] = chr(nextEmpty)
	nextEmpty+=1
	return chr(nextEmpty-1)

def hideSpans(mainstring):
	returnString = mainstring
	spans = BeautifulSoup(mainstring,'html.parser').findAll('span')
	for span in spans:
		spanString = str(span)
		replacementChar = storeinmap(spanString)
		returnString = returnString.replace(spanString, replacementChar)

	replaceDict={}
	matches = re.findall(r'<([^>]*)>', returnString)
	for match in matches:
		replacement=storeinmap('<'+match+'>')
		returnString = returnString.replace('<'+match+'>',replacement)

	return returnString

def unhideSpans(mainstring):
	global mapdict
	res = mainstring
	for key in mapdict.keys():
		res = res.replace(key,mapdict[key])
	return res

def getTitles():
	return open(f'{linuxDir}err_titles_full.txt','r',encoding='utf-8').read().split('\n')

def clean_page(html):
	soup = BeautifulSoup(html,'html.parser')
	tables = soup.findAll('table')
	actags = soup.findAll('ac:structured-macro')
	acsection = soup.findAll('ac:layout-section')
	ahref = soup.findAll('a')

	if tables:
		for item in tables:
			item.extract()
			# print(len(str(item)))
			# print(len(str(item)))
			pass
	if actags:
		for item in actags:
			item.extract()
	if ahref:
		for item in ahref:
			item.extract()
	if len(acsection)>=2:
		acsection[0].extract()
	# return str(soup)
	contentbody = soup.find('ac:layout-cell')
	if contentbody:
		contents = contentbody.contents
		string=''
		for item in contents:
			string+=str(item)
		return string
	else:
		return str(soup)

def unwrap_useless_spans(htmlstring):
	soup = BeautifulSoup(htmlstring,'html.parser')
	for i in range(10):
		spans = soup.findAll('span')
		for span in spans:
			if span.has_attr('class'):
				if 'tcode' in span['class']:
					pass
				else:
					span.unwrap()
			else:
				span.unwrap()
	# for tag in soup.find_all(True):  # True finds all tags
	#     if tag.name != 'span' or tag.name != 'p' or tag.name != 'br':
	#         tag.unwrap()
	return str(soup)

def remove_text_within_tcode_span(htmlstring):
	soup = BeautifulSoup(htmlstring,'html.parser')
	spans = soup.findAll('span')
	for span in spans:
		if span.text:
			span.string=''
	return str(soup)

def compare_span_tcodes(s1,s2):
	# <span class="tcode S00-12 E00-12"></span>
	hhmm = s1.split('S')[1].split(' ')[0].split('-')
	hhmm2 = s2.split('S')[1].split(' ')[0].split('-')
	v1 = int(hhmm[0])*100 + int(hhmm[1].split('_')[0])
	v2 = int(hhmm2[0])*100 + int(hhmm2[1].split('_')[0])
	if v1>v2:
		return 1
	else:
		return -1

def insert_span(mainStringSpan, newSpan, mainString):
	insertIndex = mainString.find(mainStringSpan)
	res = mainString[:insertIndex] + str(newSpan) + mainString[insertIndex:]
	# print('AFTER INSERT SPAN-',res)
	# exit()
	return unhideSpans(res)

def rearrangeDiffs(diffs):
	i=0
	res=[]
	while i<len(diffs):
		if diffs[i][0]==-1:
			if i+1 < len(diffs) and diffs[i+1][0]==1:
				res.append(diffs[i+1])
				res.append(diffs[i])
				i+=2
			else:
				res.append(diffs[i])
				i+=1
		elif diffs[i][0]==0:
			res.append(diffs[i])
			i+=1
		elif diffs[i][0]==1:
			res.append(diffs[i])
			i+=1
		else:
			print('nowhere:',diffs[i])
	return res

def remove_timestamps(string):
	time_pattern = r'\bTime(:*) \d{1,2}:\d{2}\b'
	return re.sub(time_pattern,'',string)

# total_anom1=0
def getBothPages_preProcessed(ct,title):
	# t1=clean_page(open(f'{linuxDir}whisp/wh_full_{ct}.html','r',encoding='utf-8').read())
	# t2=clean_page(open(f'{linuxDir}pr/pr_full_{ct}.html','r',encoding='utf-8').read())
	t1=clean_page(open(f'/home/<USER>/files/diff_mom/whi_384.txt','r',encoding='utf-8').read())
	t2=clean_page(open(f'/home/<USER>/files/diff_mom/pro_384.txt','r',encoding='utf-8').read())
	t1 = unwrap_useless_spans(t1)
	t2 = unwrap_useless_spans(t2)
	t1 = remove_text_within_tcode_span(t1)
	t2 = remove_text_within_tcode_span(t2)
	t1= remove_timestamps(t1)
	t2= remove_timestamps(t2)
	t1 = t1.replace(u'\xa0',' ')
	t2 = t2.replace(u'\xa0',' ')
	t1 = t1.replace('\ud800',' ')
	t2 = t2.replace('\ud800',' ')

	t1 = hideSpans(t1)
	t2 = hideSpans(t2)
	return t1, t2

def correctSpanPosition(mystring, spanString):
	indx = mystring.find(spanString)
	lenSpan = len(spanString)
	myPunct = string.punctuation.replace('<','')
	isIncorrectSpan = (mystring[indx-1].isalpha() or mystring[indx-1] in myPunct) and (mystring[indx+lenSpan].isalpha() or mystring[indx+lenSpan] in myPunct)
	# isIncorrectSpan = (mystring[indx-1].isalpha()) and (mystring[indx+lenSpan].isalpha())
	if not isIncorrectSpan:
		return mystring
	resString = mystring[:indx] + mystring[indx+lenSpan:]
	c = indx
	while True: # find next valid insert index
		if resString[c].isalpha() or resString[c] in myPunct:
			c+=1
		else:
			break
	resString=resString[:indx+c] + spanString + resString[indx+c:]
	return resString

def adjust_broken_words2(mystring):
	soup = BeautifulSoup(mystring,'html.parser')
	spans = soup.findAll('span')
	# resString = mystring
	# for span in reversed(spans):
	for span in spans:
		mystring = correctSpanPosition(mystring,str(span))
	return mystring

def adjust_broken_words(s):
	return s

def getStringFromDiff(diffs, title, ct):
	res=''
	totalmultispans=0
	flag_hasanomaly=0
	printflag=0
	for item in diffs:
		# fanomalies.write('xyzxyzxyz\n')
		a1=int(item[0])
		a2=unhideSpans(item[1])

		# print('a1 a2 are: ',a1,a2)
		# print('res is:',res)
		if a1==0:
			res+=a2
			# print('adding:',a2)
		if a1==1:
			spans = BeautifulSoup(a2,'html.parser').findAll('span')
			if len(spans)>1:
				print(f'anomaly1----------------------------- lenSpans={len(spans)}')
				print(f'anoumalousDiff={unhideSpans( item[1])}')
				# print('title-',title)
			res+=adjust_broken_words(a2)
			# print('adding:',adjust_broken_words(a2))
		if a1==-1:
			spans = BeautifulSoup(a2,'html.parser').findAll('span')
			if len(spans)==0:
				continue
			if len(spans)==1:
				res+=str(spans[0])
				# print('adding:',str(spans[0]))
			else:
				if printflag: print(f'anomaly2----------------------------- lenSpans={len(spans)}\nanomalousdiff: {item[1]}')
				flag_hasanomaly=1
				totalmultispans+=1
				# if len(spans)>2:
				# 	flag_hasanomaly=1
				# 	print('------------------------------------doing',ct,'---------------------------')
				# 	print(title)
				# 	print(f'anomaly2----------------------------- lenSpans={len(spans)}')#\nanomalousdiff: {item[1]}')
				# 	print(f'anomalousdiff: {hideSpans( item[1])}')
				# 	print('\n')

				if printflag: print('on multispan-',totalmultispans)
				# spans = [spans[0],spans[-1]]
				for span in spans:
					reswithspan = unhideSpans(res)
					res_spans = BeautifulSoup(reswithspan,'html.parser').findAll('span')
					inserted=0

					if len(res_spans)==0:
						if printflag: print('inserted at start, len(res_spans) was 0')
						res=str(span)+res
						inserted=1
						continue
					if str(span) in reswithspan:
						if printflag: print('inserted - NOT - BCZ - already present')
						inserted=1
						continue

					if compare_span_tcodes(str(res_spans[0]),str(span)) > 0: # newSpan should come before the first span in ResSpan
						res = insert_span(str(res_spans[0]),str(span),reswithspan)
						# print('adding in between somehwere:',str(span))
						inserted=1
						if printflag: print('inserted at start')
						continue
					# else:
					for res_span in reversed(res_spans):
						if inserted==1:
							break
						span1 = str(res_span)
						span2 = str(span)
						if compare_span_tcodes(span1,span2) < 0:
							nextspan = res_span.findNext('span')
							if nextspan==None:
								res += span2
								# print('adding:',span2)
								if printflag: print('inserted at last')
							else:
								if printflag: print(f'INSERTING {span2},,,,, AFTER {span1},,,,, JUST BEFORE {str(nextspan)}')
								res = insert_span(mainStringSpan=str(nextspan), newSpan=span2, mainString=reswithspan)
								# print('adding in between somehwere:',span2)
							inserted=1

					if inserted==0:
						print('WHATTTTTTTTTT')


	if printflag: print('total multispans=',totalmultispans)
	# exit()
	return res, flag_hasanomaly

def checkcoverage(res,t1,t2):
	# test 1
	score=0
	printflag=1
	spans=BeautifulSoup(unhideSpans(t1),'html.parser').findAll('span')
	ct=0
	ct2=0
	for span in spans:
		ct2+=1
		if str(span) in res:
			ct+=1
		# print(ct2,span)
	if ct==len(spans):
		# if printflag: print(f'test 1- SUCCESS. {ct}/{len(spans)}')
		score+=1
	else:
		if printflag: print(f'test 1- NOT SUCCESS. {ct}/{len(spans)}')


	# test 2
	onlytext1 = BeautifulSoup(unhideSpans(t2),'html.parser').text.replace(u'\xa0',' ').replace('  ',' ')
	try:
		onlytext2 = BeautifulSoup(res,'html.parser').text.replace(u'\xa0',' ').replace('  ',' ')
	except TypeError:
		print('TYPE ERROR-',res)
		exit()


	diffres = fastdiff_rec(onlytext1,onlytext2,recursiondepth=0)
	# for item in diffres:
	# 	print(item,'\n\n')
	# print(onlytext1[:1000],'\n\n')
	# print(onlytext2[:1000])
	# exit()
	perfect=1
	for item in diffres:
		if item[0]==-1:
			perfect=0
			print('Test2-nonmatching-found-',item)
	if perfect==1:
		# if printflag: print('test 2- SUCCESS. Text_res == Text_proofread')
		score+=1
	else:
		if printflag: print('test 2- NOT SUCCESS. Text_res != Text_proofread')
	return score

def reformatdiffs(diffs):
	res=[]
	for item in diffs:
		aaa=[item[0],item[1]]
		# print(aaa)
		if aaa[0]=='=':
			res.append((0,aaa[1]))
		elif aaa[0]=='-':
			res.append((-1,aaa[1]))
		elif aaa[0]=='+':
			res.append((1, aaa[1]))
	return res

def fastdiff_rec(t1,t2, recursiondepth=0):
	printflag=0
	flagrecursion=1
	cleanupMode = 'Semantic'
	if recursiondepth!=0:
		cleanupMode='Efficiency'
	diffs = reformatdiffs(diff(t1,t2, counts_only=False, cleanup=cleanupMode))
	if recursiondepth>5 or flagrecursion==0:
		return diffs

	# for item in diffs:
	# 	print(item,'\n')
	# exit()

	# RECURSION
	res=[]
	i=0
	while i<len(diffs):
		if diffs[i][0]==-1:
			# spans = BeautifulSoup(unprotectspans(diffs[i][1]),'html.parser').findAll('span')
			spans = BeautifulSoup(unhideSpans(diffs[i][1]),'html.parser').findAll('span')
			length = len(spans)
			if length > 1:
				if i+1<len(diffs):
					if diffs[i+1][0] == 1:
						# print('want to do recdiff for-')
						# print('-------------1--------------')
						# print(diffs[i][1],'\n')
						# print('-------------2--------------')
						# print(diffs[i+1][1],'\n')
						if diffs[i][1] == t1 and diffs[i+1][1]==t2:
							if printflag: print('same diffs, not going for recursion')
							res.append(diffs[i])
							res.append(diffs[i+1])
							i+=2
						else:
							if printflag:
								print(f'R#{recursiondepth}. going for recursion')
								print('between')
								print(diffs[i][1])
								print('and')
								print(diffs[i+1][1])
							recdiffs = fastdiff_rec(diffs[i][1],diffs[i+1][1],recursiondepth=recursiondepth+1)
							if printflag: print(f'R#{recursiondepth}. got return diffs =',len(recdiffs))
							# recdiffs=[]
							res.extend(recdiffs)
							i+=2
					else:
						# tempres=''
						# for span in spans:
						# 	tempres+=str(span)
						res.append(diffs[i])
						i+=1
				else:
					res.append(diffs[i])
					i+=1
			else:
				res.append(diffs[i])
				i+=1
		else:
			res.append(diffs[i])
			i+=1
	return res

# whispered_SGYT_PVTyBNVttTg_Sadhguru-Darshan-LIVE-Isha-Institute-Of-Inner-Sciences-730-AM-IST-On-24-Sep-10-PM-ET-On-23-Sep_24-Sep-2024.html
def main():
	global mapdict, mapdictrev, nextEmpty
	titles = getTitles()
	start=0
	totalsuccess=0
	fails=0
	ct=start
	
	for title in titles[:100]:
		ct+=1
		title=title[:-5]
		# if ct not in [78,216,784,983,1009,1011,1137,1157,1432,2009]:
		# if ct not in [846,1568]: # NOT PROCESSED 
			# continue
		# if ct>10:
			# continue

		# if ct!=3:
			# continue
		print(f'on {ct}. success={totalsuccess}. fail={fails}')
		mapdict.clear()
		mapdictrev.clear()
		nextEmpty=19968
		# ct+=1
		t1, t2 = getBothPages_preProcessed(ct,title)#<span class="tcode S01-05-57 E01-05-57"></span>
		# if ct==10:
		# 	print(t1,'\n\n',t2)
		# 	exit()

		windowsPath = 'C:\\Users\\<USER>\\Documents\\merge-testing\\'
		if os.name=='posix':
			prePath = f'{linuxDir}merge-testing/'
		else:
			prePath = windowsPath
		lines=open(f'{prePath}fst_{ct}.html','r',encoding='utf-8').read().split('\n')
		# anomalies=open(f'{prePath}anomalies_{title}.txt','r',encoding='utf-8').read()

		# continue
		diffs=[]
		for line in lines:
			if line:
				if line[0]=='0' or line[0]=='1' or line[:2]=='-1':
					diffs.append(line)
				else:
					diffs[-1]+='\n'+line


		# print('files found',title)
		properdiffs=[]
		for item in diffs:
			if len(item.split('\t'))==2:
				properdiffs.append((int(item.split('\t')[0]),item.split('\t')[1]))

		# <span class="tcode S05-04 E05-04"></span>
		# <span class="tcode S06-38 E06-38"></span>

		# properDiffs = rearrangeDiffs(properdiffs)
		# for item in properdiffs:
			# print(item)
		# exit()

		outstring, b = getStringFromDiff(properdiffs,title,ct)
		# spans = BeautifulSoup(unhideSpans( t1),'html.parser').findAll('span')
		# ospans = BeautifulSoup(outstring,'html.parser').findAll('span')
		# print(len(spans),len(ospans))
		# for span in spans:
		# 	if span not in ospans:
		# 		print(span)

		# print(unhideSpans(t2)[:1000])
		# exit()
		# print('\n\n')
		# print(outstring[:1000])


		# exit()

		if os.name!='posix':
			continue

		# FAST DIFF AHEAD
		if checkcoverage(outstring,t1,t2) == 2:
			totalsuccess+=1
		else:
			fails+=1

		# continue
		if os.name=='posix':
			prePath = f'{linuxDir}/merge-testing/'
		else:
			prePath = windowsPath
		fout = open(f'{prePath}fmerged_{ct}.html','w',encoding='utf-8')
		fout.write(outstring)
		fout.close()

# main()

def main_ind():
	global mapdict, mapdictrev, nextEmpty
	titles=['SGYT_B9uCN3zwrBo_Mental-Health-Impact-Of-The-COVID-Pandemic​-Sadhguru-With-Medical-Experts​_13-Aug-2020']
	start=0
	totalsuccess=0
	fails=0
	ct=start
	
	for title in titles[:100]:
		ct+=1
		title=title[:-5]
		# if ct not in [78,216,784,983,1009,1011,1137,1157,1432,2009]:
		# if ct not in [846,1568]: # NOT PROCESSED 
			# continue
		# if ct>10:
			# continue

		# if ct!=3:
			# continue
		print(f'on {ct}. success={totalsuccess}. fail={fails}')
		mapdict.clear()
		mapdictrev.clear()
		nextEmpty=19968
		# ct+=1
		t1, t2 = getBothPages_preProcessed(ct,title)#<span class="tcode S01-05-57 E01-05-57"></span>
		# if ct==10:
		# 	print(t1,'\n\n',t2)
		# 	exit()

		windowsPath = 'C:\\Users\\<USER>\\Documents\\merge-testing\\'
		if os.name=='posix':
			prePath = f'{linuxDir}merge-testing/'
		else:
			prePath = windowsPath
		lines=open(f'/home/<USER>/files/diff_mom/diffs_1.txt','r',encoding='utf-8').read().split('\n')
		# anomalies=open(f'{prePath}anomalies_{title}.txt','r',encoding='utf-8').read()

		# continue
		diffs=[]
		for line in lines:
			if line:
				if line[0]=='0' or line[0]=='1' or line[:2]=='-1':
					diffs.append(line)
				else:
					diffs[-1]+='\n'+line


		# print('files found',title)
		properdiffs=[]
		for item in diffs:
			if len(item.split('\t'))==2:
				properdiffs.append((int(item.split('\t')[0]),item.split('\t')[1]))

		# <span class="tcode S05-04 E05-04"></span>
		# <span class="tcode S06-38 E06-38"></span>

		# properDiffs = rearrangeDiffs(properdiffs)
		# for item in properdiffs:
			# print(item)
		# exit()

		outstring, b = getStringFromDiff(properdiffs,title,ct)
		# spans = BeautifulSoup(unhideSpans( t1),'html.parser').findAll('span')
		# ospans = BeautifulSoup(outstring,'html.parser').findAll('span')
		# print(len(spans),len(ospans))
		# for span in spans:
		# 	if span not in ospans:
		# 		print(span)

		# print(unhideSpans(t2)[:1000])
		# exit()
		# print('\n\n')
		# print(outstring[:1000])


		# exit()

		if os.name!='posix':
			continue

		# FAST DIFF AHEAD
		if checkcoverage(outstring,t1,t2) == 2:
			totalsuccess+=1
		else:
			fails+=1

		# continue
		if os.name=='posix':
			prePath = f'{linuxDir}/merge-testing/'
		else:
			prePath = windowsPath
		fout = open(f'/home/<USER>/files/diff_mom/fmerged_{ct}.html','w',encoding='utf-8')
		fout.write(outstring)
		fout.close()

main_ind()
'''
an1file

'''
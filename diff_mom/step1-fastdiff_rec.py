import os,re
import requests
from requests.auth import HTTPBasicAuth
import json
import time
from datetime import datetime
# import diff_match_patch #as dmp_module
from bs4 import BeautifulSoup
import html.parser
if os.name=='posix':
	from fast_diff_match_patch import diff
	linuxDir='/home/<USER>/files/'
else:
	linuxDir=''

def clean_page(html):
	soup = BeautifulSoup(html,'html.parser')
	tables = soup.findAll('table')
	actags = soup.findAll('ac:structured-macro')
	acsection = soup.findAll('ac:layout-section')
	ahref = soup.findAll('a')

	if tables:
		for item in tables:
			item.extract()
			# print(len(str(item)))
			# print(len(str(item)))
			pass
	if actags:
		for item in actags:
			item.extract()
	if ahref:
		for item in ahref:
			item.extract()
	if len(acsection)>=2:
		acsection[0].extract()
	# return str(soup)
	contentbody = soup.find('ac:layout-cell')
	if contentbody:
		contents = contentbody.contents
		string=''
		for item in contents:
			string+=str(item)
		return string
	else:
		return str(soup)

mapdict={}
nextEmpty=19968
mapdictrev={}
specialchar_range = range(19968,40959)
# r = specialchar_range

def storeinmap(string):
	global nextEmpty
	if string in mapdictrev.keys():
		return mapdictrev[string]

	mapdict[chr(nextEmpty)] = string
	mapdictrev[string] = chr(nextEmpty)
	nextEmpty+=1
	return chr(nextEmpty-1)

def encodeMainString(mainstring):
	returnString = mainstring
	spans = BeautifulSoup(mainstring,'html.parser').findAll('span')
	for span in spans:
		spanString = str(span)
		replacementChar = storeinmap(spanString)
		returnString = returnString.replace(spanString, replacementChar)

	replaceDict={}
	matches = re.findall(r'<([^>]*)>', returnString)
	for match in matches:
		replacement = storeinmap('<'+match+'>')
		returnString = returnString.replace('<'+match+'>', replacement)

	return returnString

def decodeMainString(mainstring):
	global mapdict
	res = mainstring
	for key in mapdict.keys():
		res = res.replace(key,mapdict[key])
	return res

def unwrap_useless_spans(htmlstring):
	soup = BeautifulSoup(htmlstring,'html.parser')
	for i in range(10):
		spans = soup.findAll('span')
		for span in spans:
			if span.has_attr('class'):
				if 'tcode' in span['class']:
					pass
				else:
					span.unwrap()
			else:
				span.unwrap()
	# for i in range(5):
	# 	strongs = soup.findAll('strong')
	# 		for strong in strongs:
	# 			if 'Sadhguru' not in strong.text or 'Participant' not in strong.text or 'Questioner' not in strong.text:
	# 				strong.extract()
	# for i in range(5):
	# 	ems = soup.findAll('em')
	# 		for em in ems:
	# 			if em.text.strip()[0].isupper():
	# 				em.extract()

	# for tag in soup.find_all(True):  # True finds all tags
	#     if tag.name != 'span' or tag.name != 'p' or tag.name != 'br':
	#     	# if 'laughs' or 'inaudible' or 'unclear' or 'laugh' or 'audio' or 'no relevant' in tag.text:
	#     		# tag.extract()
	#         tag.unwrap()
	return str(soup)

def reformatdiffs(diffs):
	res=[]
	for item in diffs:
		aaa=[item[0],item[1]]
		# print(aaa)
		if aaa[0]=='=':
			res.append((0,aaa[1]))
		elif aaa[0]=='-':
			res.append((-1,aaa[1]))
		elif aaa[0]=='+':
			res.append((1, aaa[1]))
	return res

def fastdiff_rec(t1,t2, recursiondepth=0):
	printflag=0
	flagrecursion=1
	cleanupMode = 'Semantic'
	if recursiondepth!=0:
		cleanupMode='Efficiency'
	diffs = reformatdiffs(diff(t1,t2, counts_only=False, cleanup=cleanupMode))
	if recursiondepth>5 or flagrecursion==0:
		return diffs

	# for item in diffs:
	# 	print(item,'\n')
	# exit()

	# RECURSION
	res=[]
	i=0
	while i<len(diffs):
		if diffs[i][0]==-1:
			# spans = BeautifulSoup(unprotectspans(diffs[i][1]),'html.parser').findAll('span')
			spans = BeautifulSoup(decodeMainString(diffs[i][1]),'html.parser').findAll('span')
			length = len(spans)
			if length > 1:
				if i+1<len(diffs):
					if diffs[i+1][0] == 1:
						# print('want to do recdiff for-')
						# print('-------------1--------------')
						# print(diffs[i][1],'\n')
						# print('-------------2--------------')
						# print(diffs[i+1][1],'\n')
						if diffs[i][1] == t1 and diffs[i+1][1]==t2:
							if printflag: print('same diffs, not going for recursion')
							res.append(diffs[i])
							res.append(diffs[i+1])
							i+=2
						else:
							if printflag:
								print(f'R#{recursiondepth}. going for recursion')
								print('between')
								print(diffs[i][1])
								print('and')
								print(diffs[i+1][1])
							recdiffs = fastdiff_rec(diffs[i][1],diffs[i+1][1],recursiondepth=recursiondepth+1)
							if printflag: print(f'R#{recursiondepth}. got return diffs =',len(recdiffs))
							# recdiffs=[]
							res.extend(recdiffs)
							i+=2
					else:
						# tempres=''
						# for span in spans:
						# 	tempres+=str(span)
						res.append(diffs[i])
						i+=1
				else:
					res.append(diffs[i])
					i+=1
			else:
				res.append(diffs[i])
				i+=1
		else:
			res.append(diffs[i])
			i+=1
	return res

def remove_text_within_tcode_span(htmlstring):
	soup = BeautifulSoup(htmlstring,'html.parser')
	spans = soup.findAll('span')
	for span in spans:
		if span.text:
			span.string=''
	return str(soup)

def savediffs(diffs,ct,title):
	# f=open(f'{linuxDir}/merge-testing/fst_{ct}.html','w',encoding='utf-8')
	f=open(f'/home/<USER>/files/diff_mom/diffs_{ct}.txt','w',encoding='utf-8')
	for item in diffs:
		# try:
		f.write(str(item[0])+'\t'+item[1].replace('\ud800',' ').replace('\ud801',' ')+'\n')
		# except UnicodeEncodeError:
			# print('ERROR IN: '+str(item[0])+'\t'+item[1].replace('\ud800',' ')+'\n')
	f.close()

def remove_timestamps(string):
	time_pattern = r'\bTime(:*) \d{1,2}:\d{2}\b'
	return re.sub(time_pattern,'',string)

def getBothPages_preProcessed(ct,title):
	t1=clean_page(open(f'/home/<USER>/files/diff_mom/whi_384.txt','r',encoding='utf-8').read())
	t2=clean_page(open(f'/home/<USER>/files/diff_mom/pro_384.txt','r',encoding='utf-8').read())
	t1 = unwrap_useless_spans(t1)
	t2 = unwrap_useless_spans(t2)
	t1 = remove_text_within_tcode_span(t1)
	t2 = remove_text_within_tcode_span(t2)
	t1= remove_timestamps(t1)
	t2= remove_timestamps(t2)
	t1 = t1.replace(u'\xa0',' ')
	t2 = t2.replace(u'\xa0',' ')
	t1 = t1.replace('\ud800',' ')
	t2 = t2.replace('\ud800',' ')
	t1 = encodeMainString(t1)
	t2 = encodeMainString(t2)
	# exit()
	return t1, t2

def getTitles():
	return open(f'{linuxDir}err_titles_full.txt','r',encoding='utf-8').read().split('\n')

def main():
	# print('here2')
	total_anomalous_cases=0
	global mapdict, mapdictrev, nextEmpty
	titles = getTitles()
	start=0
	ct=start
	
	for title in titles[start:100]:
		ct+=1
		# if ct>10:
			# continue
		# print('here')
		title = title[:-5]
		mapdict.clear()
		mapdictrev.clear()
		nextEmpty=19968
		# if ct != 846:
			# continue
		print('------------------------------------doing',ct,'---------------------------tac=',total_anomalous_cases)
		# print(f'on {ct} / {len(titles)}')

		# PRE PROCESS
		t1, t2 = getBothPages_preProcessed(ct,title)
		# if ct==9:
		# 	print(t1,'\n\n',t2)
		# 	exit()
		# continue
		# MAIN
		res = fastdiff_rec(t1,t2)
		
		# for item in res:
			# print(item[0],'\t',decodeMainString(item[1]))
		# 	foo.write(str(item[0])+'\t'+item[1]+'\n')

		savediffs(res, ct, title)

# main()

def main_individual():
	# print('here2')
	total_anomalous_cases=0
	global mapdict, mapdictrev, nextEmpty
	# titles = getTitles()
	start=0
	ct=start
	titles=['SGYT_B9uCN3zwrBo_Mental-Health-Impact-Of-The-COVID-Pandemic​-Sadhguru-With-Medical-Experts​_13-Aug-2020']
	for title in titles[start:100]:
		ct+=1
		# if ct>10:
			# continue
		# print('here')
		# title = title[:-5]
		mapdict.clear()
		mapdictrev.clear()
		nextEmpty=19968
		# if ct != 846:
			# continue
		print('------------------------------------doing',ct,'---------------------------tac=',total_anomalous_cases)
		# print(f'on {ct} / {len(titles)}')

		# PRE PROCESS
		t1, t2 = getBothPages_preProcessed(ct,title)
		# if ct==9:
		# 	print(t1,'\n\n',t2)
		# 	exit()
		# continue
		# MAIN
		res = fastdiff_rec(t1,t2)
		
		# for item in res:
			# print(item[0],'\t',decodeMainString(item[1]))
		# 	foo.write(str(item[0])+'\t'+item[1]+'\n')

		savediffs(res, ct, title)

main_individual()

print('done')

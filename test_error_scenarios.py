#!/usr/bin/env python3
"""
Test script to verify error handling in assign-4.py
This script simulates various error scenarios to ensure robust error handling
"""

import sys
import os
import json
from unittest.mock import Mock, patch, MagicMock

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the module
import importlib.util
spec = importlib.util.spec_from_file_location("assign4", "assign-4.py")
assign4 = importlib.util.module_from_spec(spec)

def test_empty_customfield():
    """Test handling of empty customfield_111118"""
    print("Testing empty customfield_111118...")
    
    # Mock issue with empty customfield
    mock_issue = {
        'key': 'AT-12345',
        'fields': {
            'customfield_111118': None,  # Empty field
            'summary': 'Test Issue',
            'status': {'name': 'Open'},
            'assignee': {'name': 'testuser'},
            'components': []
        }
    }
    
    # This should return early and not crash
    try:
        assign4.update_confluence(mock_issue)
        print("✓ Empty customfield handled correctly")
        return True
    except Exception as e:
        print(f"✗ Error with empty customfield: {str(e)}")
        return False

def test_invalid_customfield():
    """Test handling of invalid customfield_111118"""
    print("Testing invalid customfield_111118...")
    
    # Mock issue with invalid customfield (not a string)
    mock_issue = {
        'key': 'AT-12345',
        'fields': {
            'customfield_111118': 12345,  # Not a string
            'summary': 'Test Issue',
            'status': {'name': 'Open'},
            'assignee': {'name': 'testuser'},
            'components': []
        }
    }
    
    try:
        assign4.update_confluence(mock_issue)
        print("✓ Invalid customfield handled correctly")
        return True
    except Exception as e:
        print(f"✗ Error with invalid customfield: {str(e)}")
        return False

def test_confluence_api_error():
    """Test handling of Confluence API errors"""
    print("Testing Confluence API error handling...")
    
    mock_issue = {
        'key': 'AT-12345',
        'fields': {
            'customfield_111118': 'https://example.com/page/test-page',
            'summary': 'Test Issue',
            'status': {'name': 'Open'},
            'assignee': {'name': 'testuser'},
            'components': []
        }
    }
    
    # Mock requests.get to return error
    mock_response = Mock()
    mock_response.status_code = 500
    mock_response.text = "Internal Server Error"
    
    with patch('requests.get', return_value=mock_response):
        try:
            assign4.update_confluence(mock_issue)
            print("✓ Confluence API error handled correctly")
            return True
        except Exception as e:
            print(f"✗ Error handling Confluence API error: {str(e)}")
            return False

def test_network_timeout():
    """Test handling of network timeouts"""
    print("Testing network timeout handling...")
    
    mock_issue = {
        'key': 'AT-12345',
        'fields': {
            'customfield_111118': 'https://example.com/page/test-page',
            'summary': 'Test Issue',
            'status': {'name': 'Open'},
            'assignee': {'name': 'testuser'},
            'components': []
        }
    }
    
    # Mock requests.get to raise timeout
    import requests
    with patch('requests.get', side_effect=requests.exceptions.Timeout("Request timed out")):
        try:
            assign4.update_confluence(mock_issue)
            print("✓ Network timeout handled correctly")
            return True
        except Exception as e:
            print(f"✗ Error handling network timeout: {str(e)}")
            return False

def test_missing_assignee():
    """Test handling of missing assignee"""
    print("Testing missing assignee handling...")
    
    mock_issue = {
        'key': 'AT-12345',
        'fields': {
            'customfield_111118': 'https://example.com/page/test-page',
            'summary': 'Test Issue',
            'status': {'name': 'Open'},
            'assignee': None,  # No assignee
            'components': []
        }
    }
    
    # Mock successful page ID retrieval
    with patch.object(assign4, 'get_page_id', return_value='12345'), \
         patch.object(assign4, 'get_current_conf_data', return_value=['transcription', '']), \
         patch.object(assign4, 'change_or_no', return_value=1), \
         patch.object(assign4, 'set_conf_status'), \
         patch('builtins.open', create=True), \
         patch.object(assign4, 'do_remove_assignee_process', return_value=''), \
         patch.object(assign4, 'worksheet') as mock_worksheet:
        
        mock_worksheet.insert_row = Mock()
        
        try:
            assign4.update_confluence(mock_issue)
            print("✓ Missing assignee handled correctly")
            return True
        except Exception as e:
            print(f"✗ Error handling missing assignee: {str(e)}")
            return False

def run_all_tests():
    """Run all error scenario tests"""
    print("Running error scenario tests for assign-4.py")
    print("=" * 60)
    
    # Initialize the module
    try:
        spec.loader.exec_module(assign4)
        # Initialize global variables
        assign4.m = {}
        assign4.totalupdates = 0
        assign4.success = 1
        print("✓ Module loaded successfully")
    except Exception as e:
        print(f"✗ Failed to load module: {str(e)}")
        return False
    
    tests = [
        test_empty_customfield,
        test_invalid_customfield,
        test_confluence_api_error,
        test_network_timeout,
        test_missing_assignee
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {str(e)}")
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All error scenarios handled correctly!")
        print("The assign-4.py script is robust and ready for production use.")
        return True
    else:
        print("✗ Some error scenarios need attention.")
        return False

if __name__ == '__main__':
    success = run_all_tests()
    sys.exit(0 if success else 1)
